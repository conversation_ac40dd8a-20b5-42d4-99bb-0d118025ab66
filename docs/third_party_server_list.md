# 获取区服列表

GET /admin-console/third_party/server_list

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|game_id|query|string|是|游戏ID|

> 请求示例

```
GET /admin-console/third_party/server_list?game_id=game_1001
```

> 返回示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 2,
    "list": [
      {
        "server_id": "s1",
        "server_name": "一区"
      },
      {
        "server_id": "s2",
        "server_name": "二区"
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|错误码|0表示成功，非0表示业务错误|
|» message|string|true|none|错误信息|错误描述信息|
|» data|object|false|none|返回数据|当失败时可能为空|
|»» total|integer|true|none|总数|区服总数|
|»» list|array|true|none|区服列表|none|
|»»» server_id|string|true|none|区服ID|none|
|»»» server_name|string|true|none|区服名称|none|

### 业务错误码（code 非0时）

|code|message示例|说明|
|---|---|---|
|400|游戏ID不能为空|缺少必填参数 game_id|
|500|获取区服列表失败|服务内部或下游调用失败|
|其它|由游戏方返回|透传游戏方错误码与信息|
