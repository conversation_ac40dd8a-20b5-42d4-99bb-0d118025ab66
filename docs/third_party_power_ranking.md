# 战力排行榜查询

GET /admin-console/third_party/power_ranking

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|game_id|query|string|是|游戏ID|
|server_id|query|string|是|区服ID|

> 请求示例

```
GET /admin-console/third_party/power_ranking?game_id=game_1001&server_id=s1
```

> 返回示例

```json
{
  "code": 0,
  "message": "success",
  "data": {
    "total": 2,
    "list": [
      {
        "game_name": "测试游戏",
        "server_name": "一区",
        "rank": 1,
        "role_id": "r_10001",
        "role_name": "剑圣一号",
        "role_level": 80,
        "power": 987654321,
        "recharge_amount_yuan": 4567.89
      },
      {
        "game_name": "测试游戏",
        "server_name": "一区",
        "rank": 2,
        "role_id": "r_10002",
        "role_name": "剑圣二号",
        "role_level": 79,
        "power": 876543210,
        "recharge_amount_yuan": 1234.56
      }
    ]
  }
}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|成功|Inline|

### 返回数据结构

状态码 **200**

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|» code|integer|true|none|错误码|0表示成功，非0表示业务错误|
|» message|string|true|none|错误信息|错误描述信息|
|» data|object|false|none|返回数据|当失败时可能为空|
|»» total|integer|true|none|总数|排行记录总数|
|»» list|array|true|none|排行列表|none|
|»»» game_name|string|true|none|游戏名称|none|
|»»» server_name|string|true|none|区服名称|none|
|»»» rank|integer|true|none|排名|从1开始|
|»»» role_id|string|true|none|角色ID|none|
|»»» role_name|string|true|none|角色名称|none|
|»»» role_level|integer|true|none|角色等级|none|
|»»» power|integer|true|none|战力|none|
|»»» recharge_amount_yuan|number|true|none|充值金额（元）|精确到分的小数|

### 业务错误码（code 非0时）

|code|message示例|说明|
|---|---|---|
|400|游戏ID不能为空|缺少必填参数 game_id|
|400|区服ID不能为空|缺少必填参数 server_id|
|500|查询战力排行失败|服务内部或下游调用失败|
|其它|由游戏方返回|透传游戏方错误码与信息|
