package bean

import "git.panlonggame.com/bkxplatform/admin-console/internal/middleware"

// PowerRankingQueryReq 战力排行查询请求
type PowerRankingQueryReq struct {
	middleware.Header
	GameID   string `json:"game_id" form:"game_id"`     // 游戏ID
	ServerID string `json:"server_id" form:"server_id"` // 区服ID
}

// PowerRankingQueryResp 战力排行查询响应
type PowerRankingQueryResp struct {
	Code int               `json:"code"`    // 错误码，0表示成功
	Msg  string            `json:"message"` // 错误信息
	Data *PowerRankingData `json:"data"`    // 返回数据
}

// PowerRankingData 战力排行数据
type PowerRankingData struct {
	Total int                `json:"total"` // 总数
	List  []PowerRankingItem `json:"list"`  // 排行列表
}

// PowerRankingItem 战力排行单项
type PowerRankingItem struct {
	GameName   string `json:"game_name"`   // 游戏名称
	ServerName string `json:"server_name"` // 区服名称
	Rank       int32  `json:"rank"`        // 战力排行，从1开始
	RoleID     string `json:"role_id"`     // 角色ID
	RoleName   string `json:"role_name"`   // 角色名称
	RoleLevel  int32  `json:"role_level"`  // 角色等级
	Power      int64  `json:"power"`       // 战力
	// DiamondCount       int32   `json:"diamond_count"`        // 钻石数量
	RechargeAmountYuan float64 `json:"recharge_amount_yuan"` // 充值金额（元），精确到分的小数
}

// PowerRankingGameRequest 向游戏方发起的请求结构
type PowerRankingGameRequest struct {
	GameID   string `json:"game_id" form:"game_id"`     // 游戏ID
	ServerID string `json:"server_id" form:"server_id"` // 区服ID
}

// PowerRankingGameResponse 游戏方返回的响应结构
type PowerRankingGameResponse struct {
	Code    int               `json:"code"`    // 错误码，0表示成功
	Message string            `json:"message"` // 错误信息
	Data    *PowerRankingData `json:"data"`    // 返回数据
}

// ServerListQueryReq 区服列表查询请求
type ServerListQueryReq struct {
	GameID string `json:"game_id" form:"game_id"` // 游戏ID
}

// ServerListQueryResp 区服列表查询响应
type ServerListQueryResp struct {
	Code int             `json:"code"`    // 错误码，0表示成功
	Msg  string          `json:"message"` // 错误信息
	Data *ServerListData `json:"data"`    // 返回数据
}

// ServerListData 区服列表数据
type ServerListData struct {
	Total int          `json:"total"` // 总数
	List  []ServerInfo `json:"list"`  // 区服列表
}

// ServerInfo 区服信息
type ServerInfo struct {
	ServerID   string `json:"server_id"`   // 区服ID
	ServerName string `json:"server_name"` // 区服名称
}

// ServerListGameRequest 向游戏方发起的区服列表请求结构
type ServerListGameRequest struct {
	// 空请求体，根据文档示例
}

// ServerListGameResponse 游戏方返回的区服列表响应结构
type ServerListGameResponse struct {
	Code    int             `json:"code"`    // 错误码，0表示成功
	Message string          `json:"message"` // 错误信息
	Data    *ServerListData `json:"data"`    // 返回数据
}
