package handler

import (
	"sync"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/logic"
	"git.panlonggame.com/bkxplatform/admin-console/internal/middleware"
	"github.com/gin-gonic/gin"
)

var (
	_powerRankingOnce    sync.Once
	_powerRankingHandler *PowerRankingHandler
)

type PowerRankingHandler struct {
	middleware.BaseHandler
	powerRankingLogic *logic.PowerRankingLogic
}

func SingletonPowerRankingHandler() *PowerRankingHandler {
	_powerRankingOnce.Do(func() {
		_powerRankingHandler = &PowerRankingHandler{
			powerRankingLogic: logic.SingletonPowerRankingLogic(),
		}
	})
	return _powerRankingHandler
}

// QueryPowerRanking 查询战力排行榜
func (h *PowerRankingHandler) QueryPowerRanking(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.PowerRankingQueryReq{}

	if !h.Bind(c, req) {
		return
	}

	resp, err := h.powerRankingLogic.QueryPowerRanking(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}

// GetServerList 获取区服列表
func (h *PowerRankingHandler) GetServerList(c *gin.Context) {
	ctx := c.Request.Context()
	req := &bean.ServerListQueryReq{}

	if !h.Bind(c, req) {
		return
	}

	resp, err := h.powerRankingLogic.GetServerList(ctx, req)
	if err != nil {
		h.Fail(c, err)
		return
	}

	h.Success(c, resp)
}
