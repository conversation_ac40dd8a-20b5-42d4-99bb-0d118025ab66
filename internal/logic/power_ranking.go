package logic

import (
	"context"

	"git.panlonggame.com/bkxplatform/admin-console/internal/handler/bean"
	"git.panlonggame.com/bkxplatform/admin-console/internal/service"
	"git.panlonggame.com/bkxplatform/admin-console/pkg/logger"
)

type PowerRankingLogic struct {
	orderService *service.OrderService
}

func SingletonPowerRankingLogic() *PowerRankingLogic {
	return &PowerRankingLogic{
		orderService: service.SingletonOrderService(),
	}
}

// QueryPowerRanking 查询战力排行榜
func (l *PowerRankingLogic) QueryPowerRanking(ctx context.Context, req *bean.PowerRankingQueryReq) (*bean.PowerRankingQueryResp, error) {
	// 参数验证
	if req.GameID == "" {
		return &bean.PowerRankingQueryResp{
			Code: 400,
			Msg:  "游戏ID不能为空",
		}, nil
	}

	if req.ServerID == "" {
		return &bean.PowerRankingQueryResp{
			Code: 400,
			Msg:  "区服ID不能为空",
		}, nil
	}

	logger.Logger.InfofCtx(ctx, "[QueryPowerRanking] 开始查询战力排行，gameid: %s, serverid: %s", req.GameID, req.ServerID)

	// 调用Service层
	result, err := l.orderService.CallPowerRanking(ctx, req.GameID, req.ServerID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[QueryPowerRanking] 调用失败，gameid: %s, serverid: %s, err: %v", req.GameID, req.ServerID, err)
		return &bean.PowerRankingQueryResp{
			Code: 500,
			Msg:  "查询战力排行失败",
		}, nil
	}

	// 检查游戏方返回的code
	if result.Code != 0 {
		logger.Logger.ErrorfCtx(ctx, "[QueryPowerRanking] 游戏方返回错误，gameid: %s, serverid: %s, code: %d, msg: %s",
			req.GameID, req.ServerID, result.Code, result.Message)
		return &bean.PowerRankingQueryResp{
			Code: result.Code,
			Msg:  result.Message,
		}, nil
	}

	// 返回成功结果
	resp := &bean.PowerRankingQueryResp{
		Code: 0,
		Msg:  "success",
		Data: result.Data,
	}

	logger.Logger.InfofCtx(ctx, "[QueryPowerRanking] 查询成功，gameid: %s, serverid: %s, total: %d",
		req.GameID, req.ServerID, result.Data.Total)

	return resp, nil
}

// GetServerList 获取区服列表
func (l *PowerRankingLogic) GetServerList(ctx context.Context, req *bean.ServerListQueryReq) (*bean.ServerListQueryResp, error) {
	// 参数验证
	if req.GameID == "" {
		return &bean.ServerListQueryResp{
			Code: 400,
			Msg:  "游戏ID不能为空",
		}, nil
	}

	logger.Logger.InfofCtx(ctx, "[GetServerList] 开始获取区服列表，gameid: %s", req.GameID)

	// 调用Service层
	result, err := l.orderService.CallServerList(ctx, req.GameID)
	if err != nil {
		logger.Logger.ErrorfCtx(ctx, "[GetServerList] 调用失败，gameid: %s, err: %v", req.GameID, err)
		return &bean.ServerListQueryResp{
			Code: 500,
			Msg:  "获取区服列表失败",
		}, nil
	}

	// 检查游戏方返回的code
	if result.Code != 0 {
		logger.Logger.ErrorfCtx(ctx, "[GetServerList] 游戏方返回错误，gameid: %s, code: %d, msg: %s",
			req.GameID, result.Code, result.Message)
		return &bean.ServerListQueryResp{
			Code: result.Code,
			Msg:  result.Message,
		}, nil
	}

	// 返回成功结果
	resp := &bean.ServerListQueryResp{
		Code: 0,
		Msg:  "success",
		Data: result.Data,
	}

	logger.Logger.InfofCtx(ctx, "[GetServerList] 获取成功，gameid: %s, total: %d",
		req.GameID, result.Data.Total)

	return resp, nil
}
