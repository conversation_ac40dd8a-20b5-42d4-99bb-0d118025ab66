package service

import (
	"context"
	"testing"

	"git.panlonggame.com/bkxplatform/admin-console/pkg/config"
	"github.com/stretchr/testify/assert"
)

func TestOrderService_CallPowerRanking(t *testing.T) {
	// 初始化配置
	config.MustInit()

	orderService := SingletonOrderService()

	tests := []struct {
		name     string
		gameID   string
		serverID string
		wantErr  bool
	}{
		{
			name:     "空游戏ID",
			gameID:   "",
			serverID: "server1",
			wantErr:  true,
		},
		{
			name:     "空区服ID",
			gameID:   "game1",
			serverID: "",
			wantErr:  true,
		},
		{
			name:     "正常参数",
			gameID:   "kof-test",
			serverID: "huaijiu_1fu",
			wantErr:  false, // 注意：这个测试可能因为网络或认证问题失败
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := orderService.CallPowerRanking(context.Background(), tt.gameID, tt.serverID)

			if tt.wantErr {
				assert.Error(t, err)
				assert.Nil(t, result)
			} else {
				// 正常情况下应该不为空，但可能因为网络问题失败
				// 这里只检查方法能正常调用
				t.Logf("Result: %+v, Error: %v", result, err)
			}
		})
	}
}
